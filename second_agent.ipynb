{"cells": [{"cell_type": "code", "execution_count": 1, "id": "de941fea", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\.conda\\envs\\cyber\\Lib\\site-packages\\tqdm\\auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["import math \n", "from typing import Optional, <PERSON><PERSON>\n", "from smolagents import tool"]}, {"cell_type": "code", "execution_count": 2, "id": "58cb4e1f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(22.817957983455095, 2)\n"]}], "source": ["@tool\n", "def calculate_cargo_travel_time(\n", "    origin_coords: <PERSON><PERSON>[float,float],\n", "    destination_coords: <PERSON><PERSON>[float,float],\n", "    cruising_speed_kmh: Optional[float] = 750.0 ,\n", ") -> float:\n", "    \"\"\"\n", "    Calculates the travel time for cargo between two coordinates.\n", "\n", "    Args:\n", "        origin_coords (Tuple[float, float]): The latitude and longitude of the origin.\n", "        destination_coords (Tuple[float, float]): The latitude and longitude of the destination.\n", "        cruising_speed_kmh (float, optional): The cruising speed in km/h. Defaults to 750.0 km/h.\n", "\n", "    Returns:\n", "        float: The travel time in hours.\n", "    \"\"\"\n", "\n", "    def to_radians(degrees: float) -> float:\n", "        return degrees * math.pi / 180.0\n", "    \n", "    #extract coordinates\n", "    lat1 , lon1 = map(to_radians, origin_coords)\n", "    lat2 , lon2 = map(to_radians, destination_coords)\n", "\n", "    EARTH_RADIUS_KM = 6371.0\n", "\n", "    # Calculate the distance using the <PERSON><PERSON><PERSON> formula\n", "    dlon = lon2 - lon1\n", "    dlat = lat2 - lat1\n", "    a = math.sin(dlat / 2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon / 2)**2\n", "    c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))\n", "    distance = EARTH_RADIUS_KM * c\n", "\n", "    # Calculate travel time\n", "    actual_distaance = distance * 1.1\n", "\n", "    #calculate flight time\n", "    flight_time = ( actual_distaance / cruising_speed_kmh ) + 1.0\n", "\n", "    return (flight_time,2)\n", "\n", "print(calculate_cargo_travel_time((41.8781, -87.6298), (-33.8688, 151.2093)))"]}, {"cell_type": "code", "execution_count": 4, "id": "b28502f0", "metadata": {}, "outputs": [], "source": ["import os\n", "from PIL import Image\n", "from smolagents import CodeAgent, GoogleSearchTool, InferenceClientModel, VisitWebpageTool\n", "\n", "model = InferenceClientModel(model_id=\"Qwen/Qwen2.5-Coder-32B-Instruct\", provider=\"together\")"]}, {"cell_type": "code", "execution_count": 8, "id": "aef8f23e", "metadata": {}, "outputs": [{"ename": "ValueError", "evalue": "Missing API key. Make sure you have 'SERPAPI_API_KEY' in your env variables.", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[8]\u001b[39m\u001b[32m, line 3\u001b[39m\n\u001b[32m      1\u001b[39m agent = CodeAgent(\n\u001b[32m      2\u001b[39m     model = model,\n\u001b[32m----> \u001b[39m\u001b[32m3\u001b[39m     tools = [\u001b[43mGoogleSearchTool\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m, VisitWebpageTool(), calculate_cargo_travel_time()],\n\u001b[32m      4\u001b[39m     additional_authorized_imports=[\u001b[33m'\u001b[39m\u001b[33mpandas\u001b[39m\u001b[33m'\u001b[39m, \u001b[33m'\u001b[39m\u001b[33mnumpy\u001b[39m\u001b[33m'\u001b[39m],\n\u001b[32m      5\u001b[39m     max_steps = \u001b[32m10\u001b[39m,\n\u001b[32m      6\u001b[39m )\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\.conda\\envs\\cyber\\Lib\\site-packages\\smolagents\\tools.py:74\u001b[39m, in \u001b[36mvalidate_after_init.<locals>.new_init\u001b[39m\u001b[34m(self, *args, **kwargs)\u001b[39m\n\u001b[32m     72\u001b[39m \u001b[38;5;129m@wraps\u001b[39m(original_init)\n\u001b[32m     73\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mnew_init\u001b[39m(\u001b[38;5;28mself\u001b[39m, *args, **kwargs):\n\u001b[32m---> \u001b[39m\u001b[32m74\u001b[39m     \u001b[43moriginal_init\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     75\u001b[39m     \u001b[38;5;28mself\u001b[39m.validate_arguments()\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\.conda\\envs\\cyber\\Lib\\site-packages\\smolagents\\default_tools.py:185\u001b[39m, in \u001b[36mGoogleSearchTool.__init__\u001b[39m\u001b[34m(self, provider)\u001b[39m\n\u001b[32m    183\u001b[39m \u001b[38;5;28mself\u001b[39m.api_key = os.getenv(api_key_env_name)\n\u001b[32m    184\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m.api_key \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m185\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mMissing API key. Make sure you have \u001b[39m\u001b[33m'\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mapi_key_env_name\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m'\u001b[39m\u001b[33m in your env variables.\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[31mValueError\u001b[39m: Missing API key. Make sure you have 'SERPAPI_API_KEY' in your env variables."]}], "source": ["agent = CodeAgent(\n", "    model = model,\n", "    tools = [GoogleSearchTool(), VisitWebpageTool(), calculate_cargo_travel_time()],\n", "    additional_authorized_imports=['pandas', 'numpy'],\n", "    max_steps = 10,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "ae965ab3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "32bf9f5b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "cyber", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 5}